//
//  AlgorithmConfiguration.swift
//  Breath2
//
//  Created by Mo on 02/06/2025.
//
//  Centralized configuration for all algorithm parameters
//  This file contains all tunable settings in one place for easy adjustment and testing

import Foundation

// MARK: - Configuration Presets Enum

enum ConfigurationPreset: String, CaseIterable {
    case paperCompliant = "Paper Compliant (STRICT)"
    case custom = "Custom"
}

// MARK: - Algorithm Configuration Structure

struct AlgorithmConfiguration: Codable {
    
    // MARK: - Audio Input Parameters
    
    /// Audio sampling frequency (Hz)
    /// Paper spec: 44,100 Hz or 48,000 Hz
    /// Impact: Higher = better accuracy, more CPU load
    var sampleRate: Float = 44100.0
    
    /// Audio buffer size in seconds
    /// Paper spec: 0.1 seconds recommended
    /// Impact: Smaller = faster updates, less accuracy
    var bufferSize: Float = 0.1
    
    // MARK: - Frequency Detection Range
    
    /// Minimum detectable frequency (Hz)
    /// Paper spec: 10 Hz (restored to paper specification)
    /// Impact: Lower = more false positives, higher = missed low-pressure breaths
    var minFreq: Int = 10
    
    /// Maximum detectable frequency (Hz)
    /// Paper spec: 40 Hz
    /// Impact: Higher = more noise sensitivity
    var maxFreq: Int = 40
    
    /// Frequency accuracy target (percentage)
    /// Paper spec: 0.025 (2.5%)
    /// Impact: Lower = more precise, higher CPU load
    var freqAccuracy: Float = 0.025
    
    // MARK: - Signal Processing Parameters
    
    /// Lower formant frequency for smoothing filter (Hz)
    /// Paper spec: 250 Hz
    /// Impact: Affects smoothing filter size calculation
    var lowerFormantFreq: Int = 250
    
    /// Minimum amplitude threshold
    /// Paper spec: 2.0E-4
    /// Impact: Filters out very weak signals
    var minAmp: Float = 2.0e-4
    
    /// Downsample factor (paper specification: fixed at 45)
    /// Paper spec: 45 (44.1kHz → 980Hz)
    /// Impact: Higher = faster processing, lower accuracy
    var downsampleFactorOverride: Int? = 45
    
    // MARK: - Autocorrelation Settings
    
    /// Correlation threshold for pitch detection
    /// Paper spec: 0.6 (restored to paper specification)
    /// Impact: Higher = fewer false positives, may miss valid detections
    var correlationThreshold: Float = 0.6
    
    /// Step size for coarse autocorrelation search
    /// Paper spec: "rough resolution" - using 3 as per paper specification
    /// Impact: Larger = faster search, may miss peaks
    var coarseStep: Int = 3
    
    /// Window size for fine autocorrelation search (±samples)
    /// Paper spec: "finer resolution" - using 2 as per paper specification
    /// Impact: Larger = more thorough, slower processing
    var fineSearchWindow: Int = 2
    
    // MARK: - Moving Average Parameters
    
    /// Decay rate for moving averages
    /// Paper spec: 0.8 (restored to paper specification)
    /// Impact: Higher = smoother output, slower adaptation
    var decayRate: Float = 0.8
    
    /// Maximum run length for moving averages
    /// Paper spec: 5 (restored to paper specification)
    /// Impact: Higher = more stable readings, slower adaptation
    var maxRunLength: Int = 5
    
    /// Leap threshold for run length reduction (percentage)
    /// Paper spec: "leap exceeds expected amount"
    /// Impact: Lower = more sensitive to changes
    var leapThreshold: Float = 0.20
    
    // MARK: - Buffer Management
    
    /// Target buffer length for pitch detection (samples at downsampled rate)
    /// Paper spec: sufficient for autocorrelation - using conservative 300 samples
    /// Impact: Larger = more accurate, higher latency
    var targetBufferLength: Int = 300
    
    /// Minimum data required before attempting detection
    /// Not specified in paper
    /// Impact: Higher = more reliable startup, longer initial delay
    var minDataCheck: Int = 100
    
    // MARK: - Pressure Model Settings
    
    /// Linear model slope (cm H2O per Hz)
    /// Paper spec: 1.119 (from research data)
    /// ⚠️ WARNING: Do not modify without clinical validation
    var pressureSlope: Float = 1.119
    
    /// Linear model intercept (cm H2O)
    /// Paper spec: -4.659 (from research data)
    /// ⚠️ WARNING: Do not modify without clinical validation
    var pressureIntercept: Float = -4.659
    
    /// Minimum valid frequency for pressure calculation (Hz)
    /// Paper spec: 10Hz minimum frequency
    var minValidFreq: Float = 10.0
    
    /// Maximum valid frequency for pressure calculation (Hz)
    var maxValidFreq: Float = 40.0
    
    /// Minimum valid pressure output (cm H2O)
    var minValidPressure: Float = 6.0
    
    /// Maximum valid pressure output (cm H2O)
    var maxValidPressure: Float = 30.0
    
    // MARK: - Advanced Settings
    
    /// Enable result saving for debugging/research
    var saveResults: Bool = false
    
    /// Custom smoothing filter size override
    /// If nil, calculated as sampleRate/lowerFormantFreq
    var smoothingFilterSizeOverride: Int? = nil
    
    /// Custom Gaussian sigma override
    /// If nil, calculated as 0.2 * downsampleFactor * maxFreq / sampleRate
    var gaussianSigmaOverride: Float? = nil
    
    // MARK: - Computed Properties (not stored)

    /// Calculated downsample factor
    func getDownsampleFactor() -> Int {
        return downsampleFactorOverride ?? Int(round(sampleRate / 980.0))
    }

    /// Calculated downsampled rate
    func getDownsampledRate() -> Float {
        return sampleRate / Float(getDownsampleFactor())
    }

    /// Calculated smoothing filter size (paper specification)
    func getSmoothingFilterSize() -> Int {
        if let override = smoothingFilterSizeOverride {
            return override
        }
        // Paper specification: sampleRate/lowerFormantFreq
        // Paper states: "176 audio sample points" for 44.1kHz
        let baseSize = Int(round(sampleRate / Float(lowerFormantFreq)))
        return baseSize // Exact paper specification without artificial minimums
    }

    /// Calculated Gaussian sigma
    func getGaussianSigma() -> Float {
        return gaussianSigmaOverride ?? (0.2 * Float(getDownsampleFactor()) * Float(maxFreq) / sampleRate)
    }

    /// Buffer size in samples
    func getBufferSizeInSamples() -> Int {
        return Int(sampleRate * bufferSize)
    }

    // MARK: - Computed Properties (for backward compatibility)

    var downsampleFactor: Int { getDownsampleFactor() }
    var downsampledRate: Float { getDownsampledRate() }
    var smoothingFilterSize: Int { getSmoothingFilterSize() }
    var gaussianSigma: Float { getGaussianSigma() }
    var bufferSizeInSamples: Int { getBufferSizeInSamples() }
    
    // MARK: - Validation
    
    /// Validates all parameters for consistency and safety
    func validate() -> (isValid: Bool, errors: [String]) {
        var errors: [String] = []
        
        // Sample rate validation
        if sampleRate < Float(2 * maxFreq) {
            errors.append("Sample rate (\(sampleRate)) must be at least 2x max frequency (\(maxFreq))")
        }
        
        // Buffer size validation
        if bufferSize <= 0 || bufferSize > 1.0 {
            errors.append("Buffer size (\(bufferSize)) must be between 0 and 1.0 seconds")
        }
        
        // Frequency range validation
        if minFreq <= 0 || minFreq >= maxFreq {
            errors.append("Frequency range invalid: minFreq (\(minFreq)) must be positive and less than maxFreq (\(maxFreq))")
        }
        
        // Correlation threshold validation
        if correlationThreshold < 0.0 || correlationThreshold > 1.0 {
            errors.append("Correlation threshold (\(correlationThreshold)) must be between 0.0 and 1.0")
        }
        
        // Decay rate validation
        if decayRate <= 0.0 || decayRate >= 1.0 {
            errors.append("Decay rate (\(decayRate)) must be between 0.0 and 1.0")
        }
        
        // Downsample validation
        if getDownsampledRate() < Float(2 * maxFreq) {
            errors.append("Downsampled rate (\(getDownsampledRate())) must satisfy Nyquist criterion for max frequency (\(maxFreq))")
        }
        
        // Search parameter validation
        if coarseStep <= 0 {
            errors.append("Coarse step (\(coarseStep)) must be positive")
        }
        
        if fineSearchWindow <= 0 {
            errors.append("Fine search window (\(fineSearchWindow)) must be positive")
        }
        
        // Buffer management validation
        if targetBufferLength <= 0 {
            errors.append("Target buffer length (\(targetBufferLength)) must be positive")
        }
        
        if minDataCheck <= 0 {
            errors.append("Min data check (\(minDataCheck)) must be positive")
        }
        
        return (errors.isEmpty, errors)
    }
    
    // MARK: - Description
    
    /// Human-readable description of current configuration
    var description: String {
        return """
        Algorithm Configuration:
        - Sample Rate: \(sampleRate) Hz
        - Buffer Size: \(bufferSize) sec (\(getBufferSizeInSamples()) samples)
        - Frequency Range: \(minFreq)-\(maxFreq) Hz
        - Downsample Factor: \(getDownsampleFactor()) (\(sampleRate)Hz → \(getDownsampledRate())Hz)
        - Correlation Threshold: \(correlationThreshold)
        - Decay Rate: \(decayRate)
        - Target Buffer Length: \(targetBufferLength) samples
        - Smoothing Filter Size: \(getSmoothingFilterSize()) samples
        """
    }
}

// MARK: - Preset Configurations

extension AlgorithmConfiguration {
    
    /// Standard configuration (STRICT paper specifications ONLY)
    static let standard = AlgorithmConfiguration(
        sampleRate: 44100.0,
        bufferSize: 0.1,
        minFreq: 10,
        maxFreq: 40,
        freqAccuracy: 0.025,
        lowerFormantFreq: 250,
        minAmp: 2.0e-4,
        downsampleFactorOverride: 45,
        correlationThreshold: 0.6,
        coarseStep: 3,
        fineSearchWindow: 2,
        decayRate: 0.8,
        maxRunLength: 5,
        leapThreshold: 0.20,
        targetBufferLength: 300,
        minDataCheck: 100,
        pressureSlope: 1.119,
        pressureIntercept: -4.659,
        minValidFreq: 10.0,
        maxValidFreq: 40.0,
        minValidPressure: 6.0,
        maxValidPressure: 30.0,
        saveResults: false,
        smoothingFilterSizeOverride: nil
    )

    /// Paper-compliant configuration (exact paper specifications)
    static let paperCompliant = AlgorithmConfiguration(
        sampleRate: 44100.0,
        bufferSize: 0.1,
        minFreq: 10,
        maxFreq: 40,
        freqAccuracy: 0.025,
        lowerFormantFreq: 250,
        minAmp: 2.0e-4,
        downsampleFactorOverride: 45,
        correlationThreshold: 0.6,
        coarseStep: 3,
        fineSearchWindow: 2,
        decayRate: 0.8,
        maxRunLength: 5,
        leapThreshold: 0.20,
        targetBufferLength: 300,
        minDataCheck: 100,
        pressureSlope: 1.119,
        pressureIntercept: -4.659,
        minValidFreq: 10.0,
        maxValidFreq: 40.0,
        minValidPressure: 6.0,
        maxValidPressure: 30.0,
        saveResults: false,
        smoothingFilterSizeOverride: nil
    )
    
    // ALL NON-COMPLIANT PRESETS REMOVED
    // ONLY PAPER-COMPLIANT CONFIGURATION IS ALLOWED
    
    /// Get configuration for a specific preset (ONLY paper-compliant allowed)
    static func configuration(for preset: ConfigurationPreset) -> AlgorithmConfiguration {
        switch preset {
        case .paperCompliant:
            return .paperCompliant
        case .custom:
            return .paperCompliant // Default to paper-compliant for custom
        }
    }
}
